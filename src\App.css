body {
  margin: 0;
  font-family: system-ui, sans-serif;
  background: #111;
  color: #f9f9f9;
}

.app {
  max-width: 700px;
  margin: 0 auto;
  padding: 2rem;
}

h1 {
  text-align: center;
  margin-bottom: 2rem;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #222;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.search-bar .icon {
  margin-right: 0.5rem;
  color: #aaa;
}

.search-bar input {
  background: transparent;
  border: none;
  outline: none;
  color: #fff;
  flex: 1;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.filters h4 {
  margin: 0.5rem 0 0.25rem;
  font-size: 0.9rem;
  color: #ccc;
  text-transform: uppercase;
}

.filters button {
  padding: 0.3rem 0.8rem;
  border: 1px solid #555;
  border-radius: 20px;
  background: #222;
  color: #ccc;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, transform 0.2s;
}

.filters button:hover {
  border-color: #08f;
  color: #08f;
}

.filters button.active {
  background: #08f;
  border-color: #08f;
  color: #fff;
}

.filters button:active {
  transform: scale(0.9);
}

.trick-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.trick-item {
  background: #1a1a1a;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  transition: background 0.2s, transform 0.2s, opacity 0.3s ease-in-out;
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.3s forwards;
}

.trick-item:hover {
  background: #2a2a2a;
}

.trick-name {
  font-weight: bold;
}

.trick-meta {
  font-size: 0.85rem;
  color: #aaa;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.video-link {
  font-size: 0.8rem;
  color: #08f;
  margin-left: 0.5rem;
  text-decoration: none;
}

.video-link:hover {
  text-decoration: underline;
}

.empty {
  text-align: center;
  color: #777;
  margin-top: 2rem;
}

mark {
  background-color: #08f3;
  color: #000;
  padding: 0 0.2rem;
  border-radius: 2px;
}