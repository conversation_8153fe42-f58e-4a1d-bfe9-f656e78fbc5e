body {
  margin: 0;
  font-family: system-ui, sans-serif;
  background: #111;
  color: #f9f9f9;
}

.app {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

h1 {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  background: linear-gradient(135deg, #08f, #0066cc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #222;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.search-bar .icon {
  margin-right: 0.5rem;
  color: #aaa;
}

.search-bar input {
  background: transparent;
  border: none;
  outline: none;
  color: #fff;
  flex: 1;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #333;
}

.filter-header button {
  padding: 0.75rem 1.5rem;
  border: 2px solid #444;
  border-radius: 8px;
  background: #2a2a2a;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-header button:hover {
  border-color: #08f;
  background: #08f;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 136, 255, 0.3);
}

.filter-header button:active {
  transform: translateY(0);
}

.filters {
  background: #1a1a1a;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #333;
}

.filter-section {
  margin-bottom: 1.5rem;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.85rem;
  color: #08f;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 1px;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #333;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-buttons button {
  padding: 0.5rem 1rem;
  border: 2px solid #444;
  border-radius: 25px;
  background: #2a2a2a;
  color: #ccc;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
}

.filter-buttons button:hover {
  border-color: #08f;
  color: #08f;
  background: #333;
  transform: translateY(-1px);
}

.filter-buttons button.active {
  background: linear-gradient(135deg, #08f, #0066cc);
  border-color: #08f;
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 136, 255, 0.4);
  transform: translateY(-1px);
}

.filter-buttons button:active {
  transform: scale(0.95);
}

.trick-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.trick-item {
  background: #1a1a1a;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  transition: background 0.2s, transform 0.2s, opacity 0.3s ease-in-out;
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.3s forwards;
}

.trick-item:hover {
  background: #2a2a2a;
}

.trick-name {
  font-weight: bold;
}

.trick-meta {
  font-size: 0.85rem;
  color: #aaa;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.video-link {
  font-size: 0.8rem;
  color: #08f;
  margin-left: 0.5rem;
  text-decoration: none;
}

.video-link:hover {
  text-decoration: underline;
}

.empty {
  text-align: center;
  color: #777;
  margin-top: 2rem;
}

mark {
  background-color: #08f3;
  color: #000;
  padding: 0 0.2rem;
  border-radius: 2px;
}