import { useEffect, useState } from "react";
import { FiSearch } from "react-icons/fi";
import "./App.css";

export default function App() {
  const [tricks, setTricks] = useState([]);
  const [search, setSearch] = useState("");
  const [filters, setFilters] = useState([]);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetch("/tricks.json")
      .then((res) => res.json())
      .then((data) => setTricks(data));
  }, []);

  const toggleFilter = (f) => {
    setFilters((prev) =>
      prev.includes(f) ? prev.filter((x) => x !== f) : [...prev, f]
    );
  };

  const filteredTricks = tricks.filter((t) => {
    const matchesSearch = t.name.toLowerCase().includes(search.toLowerCase());

    const stanceFilters = filters.filter((f) =>
      ["regular", "fakie", "switch", "nollie"].includes(f)
    );
    const typeFilters = filters.filter(
      (f) => !["regular", "fakie", "switch", "nollie"].includes(f)
    );

    let matchesStance =
      stanceFilters.length === 0 ||
      stanceFilters.includes(t.stance.toLowerCase());
    let matchesType =
      typeFilters.length === 0 ||
      t.type.some((ty) => typeFilters.includes(ty.toLowerCase()));

    // Apply stance-first logic: if a stance is selected, type filters only apply within that stance
    if (stanceFilters.length > 0) {
      matchesType =
        typeFilters.length === 0 ||
        (matchesStance &&
          t.type.some((ty) => typeFilters.includes(ty.toLowerCase())));
    }

    return matchesSearch && matchesStance && matchesType;
  });

  // Highlight matching search term in trick name
  const highlight = (text) => {
    if (!search) return text;
    const regex = new RegExp(`(${search})`, "gi");
    return text.replace(regex, "<mark>$1</mark>");
  };

  return (
    <div className="app">
      <h1>Flatground Trick Index</h1>

      {/* Search */}
      <div className="search-bar">
        <FiSearch className="icon" />
        <input
          type="text"
          placeholder="Search tricks..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      {/* Filter Header with toggle & clear */}
      <div className="filter-header">
        <button onClick={() => setShowFilters(!showFilters)}>
          {showFilters ? "Hide Filters" : "Show Filters"}
        </button>
        <button onClick={() => setFilters([])}>Clear Filters</button>
      </div>

      {showFilters && (
        <div className="filters">
          <div className="filter-section">
            <h4>Stances</h4>
            <div className="filter-buttons">
              {["regular", "fakie", "switch", "nollie"].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}
                </button>
              ))}
            </div>
          </div>

          <div className="filter-section">
            <h4>Rotations</h4>
            <div className="filter-buttons">
              {["180", "360", "540"].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}°
                </button>
              ))}
            </div>
          </div>

          <div className="filter-section">
            <h4>Flip Types</h4>
            <div className="filter-buttons">
              {[
                "flip",
                "kickflip",
                "heelflip",
                "varial",
                "pressure",
                "hardflip",
                "inward",
                "lazer",
                "feather flip",
                "forward flip",
                "impossible",
                "double",
                "triple",
                "anti casper",
              ].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}
                </button>
              ))}
            </div>
          </div>

          <div className="filter-section">
            <h4>Shuv / Spins</h4>
            <div className="filter-buttons">
              {[
                "shuv",
                "pop shove-it",
                "big spin",
                "big flip",
                "big heelflip",
                "biggerspin",
                "gazelle spin",
                "body varial",
                "antibigspin",
              ].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}
                </button>
              ))}
            </div>
          </div>

          <div className="filter-section">
            <h4>Other / Modifiers</h4>
            <div className="filter-buttons">
              {[
                "late",
                "front foot",
                "underflip",
                "cancel",
                "plasma",
                "half",
                "big",
                "bigger",
              ].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
      {/* Trick List */}
      {filteredTricks.length === 0 ? (
        <p className="empty">No tricks found 😔</p>
      ) : (
        <ul className="trick-list">
          {filteredTricks.map((t) => (
            <li key={t.id} className="trick-item">
              <div
                className="trick-name"
                dangerouslySetInnerHTML={{ __html: highlight(t.name) }}
              />
              <div className="trick-meta">
                {t.stance} • {t.type.join(", ")} •
                <a
                  href={`/videos/${t.video}.mp4`}
                  className="video-link"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  🎥 Watch
                </a>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
