I am working on a React + Tailwind web app called Flatground Trick Index. It lists skateboarding tricks in a searchable and filterable format. The tricks are stored in a JSON file with the following structure:

{
  "id": 1,
  "name": "Kickflip",
  "stance": "regular",
  "type": ["flip"],
  "video": 1,
  "timestamp": "1:53"
}


The app allows users to search by name and filter by stance, rotation, flip types, shuvs/spins, and size/complexity. The filter logic works like this:

If a stance is selected, type filters only apply to tricks with that stance.

Type filters include things like "flip", "kickflip", "heelflip", "varial", "pressure", "hardflip", "inward", "lazer", "feather flip", "forward flip", "impossible", "double", "triple".

Spin/shuv filters include "shuv", "pop shove-it", "big spin", "big flip", "big heelflip", "biggerspin", "gazelle spin", "body varial".

Size/complexity filters include "big", "bigger", "half", "plasma".

Rotations: "180", "360", "540".

My JSON contains a growing list of tricks. I want to continue classifying new tricks into stance, type, video, and timestamp fields, adding new filter tags where needed.

When I provide the next batch of tricks (with timestamps), I want you to:

Analyze them for new filter categories that should be added.

Update their JSON entries with proper stance and type arrays.

Ensure consistency with my existing classification and naming conventions.

Output the updated JSON for the new batch.

Do not ask me to re-explain the project. Use this context for all future trick classification.